<template>
  <!-- 自组作业 -->
  <div class="custom_assign_container">
    <el-container class="main_container">
      <!-- 左侧面板 -->
      <el-aside width="440px" class="left_panel">
        <div class="panel_header">
          <el-select
            v-model="form.periodSubjectName"
            placeholder="请选择"
            @change="handlePeriodChange"
            :loading="loading.periodSubject"
          >
            <el-option
              v-for="item in periodAndSubject"
              :key="item.periodSubjectName"
              :label="item.periodSubjectName"
              :value="item.periodSubjectName"
            >
            </el-option>
          </el-select>
        </div>
        <el-tabs v-model="activeTab" class="content_tabs">
          <el-tab-pane label="章节" name="chapters">
            <div class="press-volume-container">
              <el-select
                v-model="form.press"
                placeholder="请选择版本"
                @change="handleVersionChange"
                class="edition_select"
                :loading="loading.versions"
                :disabled="!versions.length"
              >
                <el-option
                  v-for="item in versions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
              <el-select
                v-model="form.grade"
                placeholder="请选择年级"
                class="edition_select"
                @change="handleGradeChange"
                :loading="loading.grades"
                :disabled="!grades.length"
              >
                <el-option
                  v-for="item in grades"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>

              <el-select
                v-model="form.volume"
                placeholder="请选择册次"
                class="edition_select"
                @change="handleVolumeChange"
                :loading="loading.volumes"
                :disabled="!volumes.length"
              >
                <el-option
                  v-for="item in volumes"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </div>
            <el-tree
              v-if="!loading.chapters && chapterData.length"
              ref="chapterTreeRef"
              :data="chapterData"
              :props="defaultProps"
              node-key="id"
              default-expand-all
              class="chapter_tree"
              :current-node-key="selectedChapterNode?.id"
              :highlight-current="true"
              @node-click="handleChapterCheck"
            >
              <template #default="{ node }">
                <el-popover
                  placement="right"
                  :width="200"
                  trigger="hover"
                  :content="node.label"
                  :disabled="!checkIfOverflow"
                >
                  <template #reference>
                    <span
                      class="custom-tree-node"
                      :ref="(el) => setNodeRef(el, node.id)"
                      @mouseenter="() => checkOverflow(node.id)"
                    >
                      <span>{{ node.label }}</span>
                    </span>
                  </template>
                </el-popover>
              </template>
            </el-tree>
            <el-empty
              v-else-if="!loading.chapters"
              description="暂无章节数据"
              :image="noDataImg"
            ></el-empty>
            <div v-else class="loading-placeholder">
              <el-skeleton :rows="6" animated />
            </div>
          </el-tab-pane>
          <el-tab-pane label="知识点" name="knowledge">
            <div class="knowledge_panel_content">
              <el-input
                v-model="knowledgeSearchQuery"
                placeholder="搜索知识点"
                class="knowledge_search_input"
                @input="debouncedSearchKnowledge"
                @clear="searchKnowledge"
                clearable
              >
                <template #append>
                  <el-button @click="searchKnowledge">搜索</el-button>
                </template>
              </el-input>
              <h3 class="knowledge_title">全部知识点</h3>
              <el-tree
                v-if="!loading.knowledge && knowledgeData.length"
                ref="knowledgeTreeRef"
                :data="knowledgeData"
                :props="defaultProps"
                node-key="id"
                default-expand-all
                class="knowledge_tree"
                :current-node-key="selectedKnowledgeNode?.id"
                :highlight-current="true"
                @node-click="handleKnowledgeCheck"
              >
                <template #default="{ node }">
                  <el-popover
                    placement="right"
                    :width="200"
                    trigger="hover"
                    :content="node.label"
                    :disabled="!checkIfOverflow"
                  >
                    <template #reference>
                      <span
                        class="custom-tree-node"
                        :ref="(el) => setNodeRef(el, node.id)"
                        @mouseenter="() => checkOverflow(node.id)"
                      >
                        <span>{{ node.label }}</span>
                      </span>
                    </template>
                  </el-popover>
                </template>
              </el-tree>
              <el-empty
                v-else-if="!loading.knowledge"
                description="暂无知识点数据"
              ></el-empty>
              <div v-else class="loading-placeholder">
                <el-skeleton :rows="6" animated />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-aside>
      <!-- 右侧面板 -->
      <el-main class="right_panel">
        <!-- 筛选区域 -->
        <div class="filter_section">
          <el-row :gutter="20" align="middle">
            <el-col :span="1" class="filter_label">题型:</el-col>
            <el-col :span="23">
              <el-radio-group v-model="questionType">
                <el-radio-button
                  key="all"
                  label="全部"
                  value=""
                  @click="handleQuestionTypeChange('')"
                  >全部</el-radio-button
                >
                <el-radio-button
                  v-for="item in questionTypes"
                  :key="item.dictQuestionType"
                  :label="item.dictQuestionTypeName"
                  :value="item.dictQuestionType"
                  @click="handleQuestionTypeChange(item.dictQuestionType)"
                  >{{ item.dictQuestionTypeName }}</el-radio-button
                >
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="20" align="middle" class="difficulty_row">
            <el-col :span="1" class="filter_label">难度:</el-col>
            <el-col :span="23">
              <el-radio-group v-model="difficulty">
                <el-radio-button
                  v-for="item in task_difficulty"
                  :key="item.value"
                  :label="item.value"
                  @click="handleDifficultyChange(item.value)"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
            </el-col>
          </el-row>
        </div>
        <!-- 题目列表 -->
        <div v-if="!loading.questions" class="question_list">
          <div v-if="questions.length === 0" class="empty-questions">
            <el-empty
              description="暂无题目数据，请选择筛选条件"
              :image="noDataImg"
            ></el-empty>
          </div>
          <div v-else>
            <QuestionList
              :questions="questions"
              @view-analysis="handleViewAnalysis"
              :basketCountIds="basketCountIds"
              @question-basket-change="fetchQuestionBasketCount"
            >
              <template #pagination>
                <el-pagination
                  background
                  layout="prev, pager, next, jumper, ->, total"
                  :total="totalQuestions"
                  v-model:current-page="currentPage"
                  :page-size="pageSize"
                  @current-change="handlePageChange"
                >
                </el-pagination>
              </template>
            </QuestionList>
          </div>
        </div>
        <div v-else class="loading-questions">
          <el-skeleton :rows="10" animated />
        </div>
      </el-main>
    </el-container>
    <!-- 使用全局试题篮组件 -->
    <question-basket
      :count="basketCountIds.length"
      @basket-click="handleBasketClick"
    />
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  onMounted,
  watch,
  nextTick,
  onBeforeUnmount,
} from "vue";

import { ElMessage } from "element-plus";
// 导入API接口
import {
  getQuestionType,
  getPeriodAndSubject,
  getChapterTree,
  getKnowledgeTree,
  getQuestion,
} from "@/api/custom-assign";

import { getPresses, getVolumes, getGrades } from "@/api/ready-study";
import { getQuestionBasketQuestionId, questionBasketList } from "@/api/question-basket";
import QuestionList from "./components/QuestionList.vue";
import noDataImg from "@/assets/images/no-data.png";

// 导入 Pinia store
import useCustomAssignStore from "@/store/modules/customAssign";

// 引入字典
const { proxy } = getCurrentInstance();
const { task_difficulty } = proxy.useDict("task_difficulty");

// 使用 Pinia store
const customAssignStore = useCustomAssignStore();

// 搜索与筛选数据
const knowledgeSearchQuery = ref("");
// 题型
const questionType = ref("");
// 难度
const difficulty = ref("0");

// 知识点相关状态
const originalKnowledgeData = ref([]); // 存储原始完整的知识点数据
const isKnowledgeDataLoaded = ref(false); // 标记是否已加载过知识点数据
// 标签页
const activeTab = ref("chapters");

const questionBasketInfo = ref({
  period: '',
  subject: '',
});

provide('questionBasketInfo', questionBasketInfo);

// 表单数据
const form = reactive({
  periodSubjectName: "",
  period: "",
  subject: "",
  press: "",
  grade: "",
  volume: "",
  selectedChapter: "",
  selectedKnowledges: "",
});

// 选中状态管理
const selectedChapterNode = ref(null);
const selectedKnowledgeNode = ref(null);

// 树组件引用
const chapterTreeRef = ref(null);
const knowledgeTreeRef = ref(null);

// 加载状态
const loading = reactive({
  periodSubject: false,
  versions: false,
  grades: false,
  volumes: false,
  chapters: false,
  knowledge: false,
  questions: false,
});

// 数据源
const periodAndSubject = ref([]);
const versions = ref([]);
const grades = ref([]);
const volumes = ref([]);
const chapterData = ref([]);
const knowledgeData = ref([]);
const questionTypes = ref([]);
const questions = ref([]);

// 分页数据
const currentPage = ref(1);
const pageSize = ref(10);
const totalQuestions = ref(0);

// 试题篮已添加的试题列表
const basketCountIds = ref([]);

// 树形控件配置
const defaultProps = {
  children: "children",
  label: "name",
};

// 存储节点引用
const nodeRefs = ref(new Map());
// 存储溢出状态
const checkIfOverflow = ref(false);

// 设置节点引用
const setNodeRef = (el, nodeId) => {
  if (el) {
    nodeRefs.value.set(nodeId, el);
  }
};

// 检查文本是否溢出（通过检测省略号）
const checkOverflow = (nodeId) => {
  const element = nodeRefs.value.get(nodeId);
  if (element) {
    // 检查元素的 scrollWidth 是否大于 clientWidth
    // 如果是，说明文本被截断了，会显示省略号
    checkIfOverflow.value = element.scrollWidth > element.clientWidth;
  }
};

// 添加组件卸载标志
const isUnmounted = ref(false);

onBeforeUnmount(() => {
  isUnmounted.value = true;
  // 清理防抖定时器
  if (saveTimer) {
    clearTimeout(saveTimer);
    saveTimer = null;
  }
  // 页面卸载时保存最终状态到 Pinia
  saveFiltersToStore();
  // 设置导航标识，表示是正常页面跳转而不是刷新
  sessionStorage.setItem('custom_assign_navigating', 'true');
});

/**
 * 获取学段学科数据
 */
const getPeriodAndSubjectData = async () => {
  loading.periodSubject = true;
  try {
    const res = await getPeriodAndSubject();
    if (res.code === 200) {
      periodAndSubject.value = res.data || [];

      // 默认选择第一个学段学科
      if (periodAndSubject.value.length > 0) {
        form.periodSubjectName = periodAndSubject.value[0].periodSubjectName;
        form.period = periodAndSubject.value[0].period;

        // 自动触发学段学科变化事件
        handlePeriodChange(form.periodSubjectName);
      }
    } else {
      ElMessage.error(res.msg || "获取学段学科数据失败");
    }
  } catch (error) {
    console.error("获取学段学科数据出错:", error);
    ElMessage.error("获取学段学科数据出错");
  } finally {
    loading.periodSubject = false;
  }
};

/**
 * 学段学科选择处理
 * @param {string|number} value - 学段学科ID
 */
const handlePeriodChange = async (value) => {
  // 获取当前选中的学段学科详细信息
  const currentPeriodSubject = periodAndSubject.value.find(
    (item) => item.periodSubjectName === value
  );
  if (!currentPeriodSubject) return;
  const { period, subject, id } = currentPeriodSubject;
  form.period = period;
  form.subject = subject;

  // 确保period和subject都已设置后才调用fetchQuestionBasketCount
  if (form.period && form.subject) {
    // 如果存在fetchQuestionBasketCount方法，则调用它
    fetchQuestionBasketCount();
  }

  // 获取题型列表
  await getQuestionTypesData(id);

  // 获取版本列表
  await getVersionsData(period, subject);

  // 重置知识点数据加载状态，确保切换学科时重新加载知识点
  isKnowledgeDataLoaded.value = false;
  originalKnowledgeData.value = [];
  knowledgeData.value = [];
  knowledgeSearchQuery.value = "";

  // 如果当前在知识点标签页，重新加载知识点数据
  if (activeTab.value === "knowledge") {
    await searchKnowledge();
  }

  // 清空相关数据
  resetFilterData();
};

/**
 * 获取题型列表数据
 * @param {string|number} periodSubjectId - 学段学科ID
 */
const getQuestionTypesData = async (id) => {
  try {
    const res = await getQuestionType(id);
    if (res.code === 200) {
      questionTypes.value = res.data || [];
      // questionType 保持默认的空字符串，不自动选择第一个题型
    } else {
      ElMessage.error(res.msg || "获取题型列表失败");
    }
  } catch (error) {
    console.error("获取题型列表出错:", error);
    ElMessage.error("获取题型列表出错");
  }
};

/**
 * 获取版本列表数据
 * @param {string|number} period - 学段
 * @param {string|number} subject - 学科
 */
const getVersionsData = async (period, subject) => {
  loading.versions = true;
  try {
    const res = await getPresses(period, subject);
    if (res.code === 200) {
      versions.value = res.data || [];

      // 重置版本相关数据
      form.press = "";
      form.grade = "";
      form.volume = "";
      grades.value = [];
      volumes.value = [];
    } else {
      ElMessage.error(res.msg || "获取版本列表失败");
    }
  } catch (error) {
    console.error("获取版本列表出错:", error);
    ElMessage.error("获取版本列表出错");
  } finally {
    loading.versions = false;
  }
};

/**
 * 版本变化处理
 * @param {string} value - 选中的版本
 */
const handleVersionChange = async (value) => {
  loading.grades = true;
  try {
    // 清空已选择的年级、册次和章节数据
    form.grade = "";
    form.volume = "";
    chapterData.value = [];
    grades.value = [];
    volumes.value = [];

    // 获取年级列表
    const currentPeriodSubject = periodAndSubject.value.find(
      (item) => item.periodSubjectName === form.periodSubjectName
    );
    if (!currentPeriodSubject) return;

    const { period, subject } = currentPeriodSubject;
    // 假设这里使用与获取册次类似的API，可能需要根据实际API调整
    const res = await getGrades(period, subject, value);

    if (res.code === 200) {
      grades.value = res.data || [];
    }
  } catch (error) {
    console.error("获取年级列表出错:", error);
  } finally {
    loading.grades = false;
  }
};

/**
 * 年级变化处理
 * @param {string} value - 选中的年级
 */
const handleGradeChange = async (value) => {
  loading.volumes = true;
  try {
    // 清空已选择的册次和章节数据
    form.volume = "";
    chapterData.value = [];
    volumes.value = [];

    // 获取册次列表 - 需要传入版本和年级参数
    const currentPeriodSubject = periodAndSubject.value.find(
      (item) => item.periodSubjectName === form.periodSubjectName
    );
    if (!currentPeriodSubject) return;

    const { period, subject } = currentPeriodSubject;
    // 传入年级参数获取对应册次列表
    const res = await getVolumes(period, subject, form.press, value);

    if (res.code === 200) {
      volumes.value = res.data || [];
    }
  } catch (error) {
    console.error("获取册次列表出错:", error);
  } finally {
    loading.volumes = false;
  }
};

/**
 * 册次变化处理
 * @param {string} _value - 选中的册次
 */
const handleVolumeChange = async (_value) => {
  loading.chapters = true;
  try {
    // 获取章节树数据 需要传入学段、学科、版本、年级、册次
    const { period, subject, press, grade, volume } = form;
    console.log(form);
    // 获取章节树数据
    const res = await getChapterTree({
      period,
      subject,
      press,
      grade,
      volume,
    });
    if (res.code === 200) {
      chapterData.value = res.data || [];

      // 自动选中第一个叶子节点
      if (chapterData.value.length > 0) {
        const firstLeafNode = findFirstLeafNode(chapterData.value);
        if (firstLeafNode) {
          form.selectedChapter = firstLeafNode.id;
          selectedChapterNode.value = firstLeafNode;
          // 自动获取试题
          fetchQuestions();
        }
      }
    } else {
      ElMessage.error(res.msg || "获取章节数据失败");
    }
  } catch (error) {
    console.error("获取章节数据出错:", error);
    ElMessage.error("获取章节数据出错");
  } finally {
    loading.chapters = false;
  }
};

/**
 * 章节选择处理
 * @param {object} data - 选中的节点数据
 */
const handleChapterCheck = (data) => {
  // 允许选择任何节点（包括父节点）
  form.selectedChapter = data.id;
  selectedChapterNode.value = data;
  // 清空知识点选择
  form.selectedKnowledges = "";
  selectedKnowledgeNode.value = null;
  fetchQuestions();
};

/**
 * 知识点选择处理
 * @param {object} data - 选中的节点数据
 */
const handleKnowledgeCheck = (data) => {
  // 允许选择任何节点（包括父节点）
  form.selectedKnowledges = data.id;
  selectedKnowledgeNode.value = data;
  // 清空章节选择
  form.selectedChapter = "";
  selectedChapterNode.value = null;
  fetchQuestions();
};

/**
 * 防抖搜索知识点
 */
let searchTimer = null;
const debouncedSearchKnowledge = () => {
  if (searchTimer) {
    clearTimeout(searchTimer);
  }
  searchTimer = setTimeout(() => {
    searchKnowledge();
  }, 300); // 300ms 防抖
};

/**
 * 搜索知识点
 */
const searchKnowledge = async () => {
  if (!form.period || !form.subject) return;

  // 如果已经加载过数据且有搜索关键词，使用前端模糊搜索
  if (isKnowledgeDataLoaded.value && knowledgeSearchQuery.value.trim()) {
    // 前端模糊搜索
    const filteredData = filterKnowledgeTree(originalKnowledgeData.value, knowledgeSearchQuery.value);
    knowledgeData.value = filteredData;
    return;
  }

  // 如果已经加载过数据且没有搜索关键词，显示原始数据
  if (isKnowledgeDataLoaded.value && !knowledgeSearchQuery.value.trim()) {
    knowledgeData.value = originalKnowledgeData.value;
    return;
  }

  // 第一次加载或切换学段学科时，请求后端数据
  loading.knowledge = true;
  try {
    // 第一次请求不带搜索关键词，获取完整的知识点树
    const res = await getKnowledgeTree({
      period: form.period,
      subject: form.subject,
    });

    if (res.code === 200) {
      // 存储原始完整数据
      originalKnowledgeData.value = res.data || [];
      isKnowledgeDataLoaded.value = true;

      // 如果有搜索关键词，进行前端过滤
      if (knowledgeSearchQuery.value.trim()) {
        knowledgeData.value = filterKnowledgeTree(originalKnowledgeData.value, knowledgeSearchQuery.value);
      } else {
        knowledgeData.value = originalKnowledgeData.value;

        // 如果是初次加载（没有搜索关键词），自动选中第一个叶子节点
        if (knowledgeData.value.length > 0) {
          const firstLeafNode = findFirstLeafNode(knowledgeData.value);
          if (firstLeafNode) {
            form.selectedKnowledges = firstLeafNode.id;
            selectedKnowledgeNode.value = firstLeafNode;
            // 清空章节选择
            form.selectedChapter = "";
            selectedChapterNode.value = null;
            // 自动获取试题
            fetchQuestions();
          }
        }
      }
    } else {
      ElMessage.error(res.msg || "搜索知识点失败");
    }
  } catch (error) {
    console.error("搜索知识点出错:", error);
    ElMessage.error("搜索知识点出错");
  } finally {
    loading.knowledge = false;
  }
};

/**
 * 分页切换处理
 * @param {number} page - 当前页
 */
const handlePageChange = (page) => {
  currentPage.value = page;
  fetchQuestions();
};

/**
 * 查找第一个叶子节点
 * @param {Array} nodes - 节点数组
 * @returns {Object|null} - 第一个叶子节点
 */
const findFirstLeafNode = (nodes) => {
  for (const node of nodes) {
    // 如果没有子节点，说明是叶子节点
    if (!node.children || node.children.length === 0) {
      return node;
    }
    // 如果有子节点，递归查找
    const leafNode = findFirstLeafNode(node.children);
    if (leafNode) {
      return leafNode;
    }
  }
  return null;
};

/**
 * 前端模糊搜索知识点树
 * @param {Array} nodes - 知识点树节点数组
 * @param {string} keyword - 搜索关键词
 * @returns {Array} - 过滤后的节点数组
 */
const filterKnowledgeTree = (nodes, keyword) => {
  if (!keyword || !keyword.trim()) {
    return nodes;
  }

  const searchKeyword = keyword.toLowerCase().trim();

  const filterNode = (node) => {
    // 检查当前节点名称是否匹配
    const nodeMatches = node.name && node.name.toLowerCase().includes(searchKeyword);

    // 递归过滤子节点
    let filteredChildren = [];
    if (node.children && node.children.length > 0) {
      filteredChildren = node.children
        .map(child => filterNode(child))
        .filter(child => child !== null);
    }

    // 如果当前节点匹配或有匹配的子节点，则保留该节点
    if (nodeMatches || filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren
      };
    }

    return null;
  };

  return nodes
    .map(node => filterNode(node))
    .filter(node => node !== null);
};

/**
 * 重置筛选数据
 */
const resetFilterData = () => {
  form.selectedChapter = "";
  form.selectedKnowledges = "";
  selectedChapterNode.value = null;
  selectedKnowledgeNode.value = null;
  form.grade = "";
  grades.value = [];
  form.volume = "";
  volumes.value = [];
  currentPage.value = 1;
  // 清空题目列表
  questions.value = [];
  totalQuestions.value = 0;
};

/**
 * 获取试题列表
 */
const fetchQuestions = async () => {
  if (isUnmounted.value) return; // 组件已卸载，直接返回

  // 检查是否有必需的参数
  if (!form.selectedChapter && !form.selectedKnowledges) {
    console.log("缺少必需参数：章节树id或知识点id");
    questions.value = [];
    totalQuestions.value = 0;
    return;
  }

  loading.questions = true;
  try {
    const queryParams = {
      chapterTreeId: form.selectedChapter,
      knowledgeIds: form.selectedKnowledges,
      questionType: questionType.value,
      difficulty: difficulty.value == 0 ? "" : difficulty.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };

    const { code, data } = await getQuestion(queryParams);

    if (isUnmounted.value) return; // 请求完成后再次检查

    if (code === 200) {
      questions.value = data.records || [];
      totalQuestions.value = data.total;
    }
  } catch (error) {
    console.error("获取试题列表出错:", error);
  } finally {
    if (!isUnmounted.value) {
      loading.questions = false;
    }
  }
};

// 获取试题篮信息
async function getQuestionBasketInfo() {
  const { period, subject } = questionBasketInfo.value;
  if (period === form.period && subject === form.subject) {
    return;
  }
  const { code, data } = await questionBasketList({
    period: form.period,
    subject: form.subject,
  });

  if (code === 200 && data) {
    questionBasketInfo.value = data;
  }
}

/**
 * 处理查看解析
 * @param {object} question - 题目
 */
const handleViewAnalysis = (question) => {
  // 这里不需要做任何处理，组件内部会处理展开/折叠
  console.log("查看解析:", question.title);
};

// 题型选择
const handleQuestionTypeChange = (type) => {
  if (!form.selectedChapter && !form.selectedKnowledges) {
    ElMessage.warning("请先选择章节或知识点");
    return;
  }
  questionType.value = type;
  fetchQuestions();
};

// 难度选择
const handleDifficultyChange = (difficultyValue) => {
  if (!form.selectedChapter && !form.selectedKnowledges) {
    ElMessage.warning("请先选择章节或知识点");
    return;
  }
  difficulty.value = difficultyValue;
  fetchQuestions();
};

// 监听period和subject的变化
watch(
  [() => form.period, () => form.subject],
  ([newPeriod, newSubject], [oldPeriod, oldSubject]) => {
    if (newPeriod && newSubject) {
      // 确保两个值都存在时更新试题篮数量
      fetchQuestionBasketCount();

      // 如果学段或学科发生变化，重置知识点数据加载状态
      if (oldPeriod !== newPeriod || oldSubject !== newSubject) {
        isKnowledgeDataLoaded.value = false;
        originalKnowledgeData.value = [];
        knowledgeData.value = [];
        knowledgeSearchQuery.value = "";
      }
    }
  },
  { immediate: true }
);

// 防抖保存到 Pinia store
let saveTimer = null;
const debounceSaveStore = () => {
  if (saveTimer) {
    clearTimeout(saveTimer);
  }
  saveTimer = setTimeout(() => {
    saveFiltersToStore();
    saveTimer = null;
  }, 500); // 500ms 防抖
};

// 监听筛选条件变化，自动保存到 Pinia store（防抖）
watch(
  [
    () => form,
    () => questionType.value,
    () => difficulty.value,
    () => activeTab.value,
    () => knowledgeSearchQuery.value,
    () => currentPage.value,
    () => selectedChapterNode.value,
    () => selectedKnowledgeNode.value,
  ],
  () => {
    debounceSaveStore();
  },
  { deep: true }
);

// 监听标签页变化，切换时重置相关状态
watch(
  () => activeTab.value,
  (newTab) => {
    if (newTab === "knowledge") {
      // 切换到知识点标签页
      // 清空章节选择
      form.selectedChapter = "";
      selectedChapterNode.value = null;
      // 清空题目列表
      questions.value = [];
      totalQuestions.value = 0;
      // 重置分页
      currentPage.value = 1;

      if (form.period && form.subject) {
        searchKnowledge();
      }
    } else if (newTab === "chapters") {
      // 切换到章节标签页
      // 清空知识点选择
      form.selectedKnowledges = "";
      selectedKnowledgeNode.value = null;
      // 清空知识点搜索
      knowledgeSearchQuery.value = "";
      // 清空题目列表
      questions.value = [];
      totalQuestions.value = 0;
      // 重置分页
      currentPage.value = 1;

      // 如果有章节数据且没有选中章节，自动选中第一个
      if (chapterData.value.length > 0 && !form.selectedChapter) {
        const firstLeafNode = findFirstLeafNode(chapterData.value);
        if (firstLeafNode) {
          form.selectedChapter = firstLeafNode.id;
          selectedChapterNode.value = firstLeafNode;
          fetchQuestions();
        }
      }
    }
  }
);

// 获取试题篮数量id
const fetchQuestionBasketCount = async () => {
  if (!form.period || !form.subject) return;

  try {
    const { code, data } = await getQuestionBasketQuestionId(
      form.period,
      form.subject
    );
    if (code === 200) {
      basketCountIds.value = data || [];
      getQuestionBasketInfo();
    } else {
      console.error("获取试题篮数据失败", data?.msg);
    }
  } catch (error) {
    console.error("获取试题篮数量失败", error);
  }
};

// 处理试题篮点击
const handleBasketClick = () => {
  // 跳转到试题篮页面
  proxy.$router.push({
    path: "/question-basket",
    query: {
      period: form.period,
      subject: form.subject,
    },
  });
};

// 监听章节数据变化，设置高亮
watch(
  () => chapterData.value,
  (newData) => {
    if (newData && newData.length > 0 && selectedChapterNode.value) {
      nextTick(() => {
        if (chapterTreeRef.value) {
          chapterTreeRef.value.setCurrentKey(selectedChapterNode.value.id);
        }
      });
    }
  },
  { flush: "post" }
);

// 监听知识点数据变化，设置高亮
watch(
  () => knowledgeData.value,
  (newData) => {
    if (newData && newData.length > 0 && selectedKnowledgeNode.value) {
      nextTick(() => {
        if (knowledgeTreeRef.value) {
          knowledgeTreeRef.value.setCurrentKey(selectedKnowledgeNode.value.id);
        }
      });
    }
  },
  { flush: "post" }
);

// 检测是否为页面刷新
const isPageRefresh = () => {
  // 使用 sessionStorage 来检测页面刷新
  // 在页面卸载时会设置一个标识，如果页面加载时没有这个标识，说明是刷新
  const isNavigating = sessionStorage.getItem('custom_assign_navigating');
  if (isNavigating) {
    // 清除标识，表示正常导航
    sessionStorage.removeItem('custom_assign_navigating');
    return false;
  }
  // 没有导航标识，说明是刷新
  return true;
};

// 页面加载时获取初始数据
onMounted(async () => {
  // 检测是否为页面刷新
  const isRefresh = isPageRefresh();
  if (isRefresh) {
    console.log('[CustomAssign] 检测到页面刷新，清除缓存');
    customAssignStore.clearCache();
  }

  // 尝试从 Pinia store 恢复缓存
  const cache = customAssignStore.getFilters();

  if (cache && !isRefresh) {
    console.log('[CustomAssign] 发现有效缓存，开始恢复...');

    // 恢复下拉选项数据
    if (cache.periodAndSubject) periodAndSubject.value = cache.periodAndSubject;
    if (cache.versions) versions.value = cache.versions;
    if (cache.grades) grades.value = cache.grades;
    if (cache.volumes) volumes.value = cache.volumes;
    if (cache.chapterData) chapterData.value = cache.chapterData;
    if (cache.knowledgeData) knowledgeData.value = cache.knowledgeData;
    if (cache.originalKnowledgeData) originalKnowledgeData.value = cache.originalKnowledgeData;
    if (cache.isKnowledgeDataLoaded !== undefined) isKnowledgeDataLoaded.value = cache.isKnowledgeDataLoaded;
    if (cache.questionTypes) questionTypes.value = cache.questionTypes;

    // 恢复表单数据和筛选条件
    if (cache.form) Object.assign(form, cache.form);
    if (cache.questionType !== undefined) questionType.value = cache.questionType;
    if (cache.difficulty !== undefined) difficulty.value = cache.difficulty;
    if (cache.activeTab !== undefined) activeTab.value = cache.activeTab;
    if (cache.knowledgeSearchQuery !== undefined) knowledgeSearchQuery.value = cache.knowledgeSearchQuery;
    if (cache.currentPage !== undefined) currentPage.value = cache.currentPage;
    if (cache.selectedChapterNode) selectedChapterNode.value = cache.selectedChapterNode;
    if (cache.selectedKnowledgeNode) selectedKnowledgeNode.value = cache.selectedKnowledgeNode;

    // 恢复题目数量信息
    if (cache.totalQuestions) totalQuestions.value = cache.totalQuestions;

    // 标记为从缓存恢复
    customAssignStore.markAsFromCache();

    console.log('[CustomAssign] 缓存恢复成功');

    // 如果有选择条件，重新获取题目数据
    if (cache.hasQuestions && (form.selectedChapter || form.selectedKnowledges)) {
      fetchQuestions();
    }
  } else {
    // 没有有效缓存或页面刷新，正常初始化
    console.log('[CustomAssign] 无有效缓存或页面刷新，执行正常初始化');
    await getPeriodAndSubjectData();
  }

  // 如果已有学段学科信息，立即获取试题篮数量
  if (form.period && form.subject) {
    fetchQuestionBasketCount();
  }
});



// 保存筛选条件到 Pinia store
const saveFiltersToStore = () => {
  const filtersToSave = {
    // 核心筛选条件
    form: { ...form },
    questionType: questionType.value,
    difficulty: difficulty.value,
    activeTab: activeTab.value,
    knowledgeSearchQuery: knowledgeSearchQuery.value,
    currentPage: currentPage.value,
    selectedChapterNode: selectedChapterNode.value,
    selectedKnowledgeNode: selectedKnowledgeNode.value,

    // 缓存下拉选项数据（必要的选项数据）
    periodAndSubject: periodAndSubject.value,
    versions: versions.value,
    grades: grades.value,
    volumes: volumes.value,
    chapterData: chapterData.value,
    knowledgeData: knowledgeData.value,
    originalKnowledgeData: originalKnowledgeData.value,
    isKnowledgeDataLoaded: isKnowledgeDataLoaded.value,
    questionTypes: questionTypes.value,

    // 只缓存题目数量，不缓存具体题目内容（减少存储空间）
    totalQuestions: totalQuestions.value,
    hasQuestions: questions.value.length > 0,
  };

  customAssignStore.saveFilters(filtersToSave);
};
</script>

<style lang="scss" scoped>
.custom_assign_container {
  height: calc(100vh - 100px); // 减去头部等高度
  background-color: #f0f2f5;
  padding: 20px;
  box-sizing: border-box;

  .main_container {
    height: 100%;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
  }
}

.left_panel {
  padding: 24px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  background: #fff;

  .panel_header {
    margin-bottom: 20px;

    .el-select {
      width: 100%;
    }
  }

  .edition_select {
    width: 100%;
  }

  // 版本和册次选择器的容器样式
  .press-volume-container {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;

    .edition_select {
      flex: 1;
      margin-bottom: 0;
    }
  }

  .content_tabs {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    :deep(.el-tabs__header) {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__active-bar) {
      display: none;
    }

    :deep(.el-tabs__nav) {
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }

    :deep(.el-tabs__item) {
      flex: 1 1 0;
      text-align: center;
      padding: 0;
      height: 36px;
      line-height: 36px;

      &.is-active {
        background-color: #00a9a2;
        color: white;
      }
    }

    :deep(.el-tabs__content) {
      flex-grow: 1;
      overflow-y: auto;
      background-color: #fff;
    }
  }

  .chapter_tree {
    margin-top: 10px;

    :deep(.el-tree-node) {
      .el-tree-node__content {
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      &.is-current > .el-tree-node__content {
        background-color: #00a9a2 !important;
        color: white !important;

        &:hover {
          background-color: #00a9a2 !important;
        }
      }
    }

    :deep(.custom-tree-node) {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
    }
  }

  .knowledge_tree {
    :deep(.el-tree-node) {
      .el-tree-node__content {
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      &.is-current > .el-tree-node__content {
        background-color: #00a9a2 !important;
        color: white !important;

        &:hover {
          background-color: #00a9a2 !important;
        }
      }
    }

    :deep(.custom-tree-node) {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
    }
  }
}

.knowledge_panel_content {
  .knowledge_search_input {
    margin-bottom: 24px;
    :deep(.el-input-group__append) {
      background-color: transparent;
      box-shadow: none;
      border-left: none;
      padding: 0 15px;
      border: 1px solid #dcdfe6;
      border-left: none;

      .el-button {
        color: #606266;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .knowledge_title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }
}

.right_panel {
  padding: 24px;
  display: flex;
  flex-direction: column;

  .filter_section {
    padding-bottom: 16px;
    border-bottom: 1px solid #e0e0e0;
    .difficulty_row {
      margin-top: 16px;
    }
    .filter_label {
      color: #333;
      font-size: 14px;
      padding-left: 0 !important;
    }

    :deep(.el-radio-button__inner) {
      border: none;
      background: transparent;
    }
    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-left: none;
    }
    :deep(.el-radio-button.is-active .el-radio-button__inner) {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      border-radius: 4px;
    }
  }

  .question_list {
    flex-grow: 1;
    overflow-y: auto;

    .empty-questions {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .question_item {
      border-bottom: 1px solid #e0e0e0;
      padding: 24px 0;

      &:last-child {
        border-bottom: none;
      }

      .question_content {
        margin: 0 0 16px;
        line-height: 1.6;
        color: #333;
      }

      .question_meta {
        display: flex;
        align-items: center;
        gap: 24px;
        font-size: 14px;
        color: #999;
        margin-bottom: 16px;

        .meta_item {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .difficulty_stars {
          // 覆盖element-plus的样式
          --el-rate-disabled-void-color: var(--el-color-info-light-7);
          height: auto;
        }
      }

      .question_actions {
        text-align: right;
      }
    }
  }

  .loading-questions,
  .loading-placeholder {
    padding: 20px 0;
  }

  .pagination_section {
    display: flex;
    justify-content: flex-end;
    padding-top: 24px;
    margin-top: auto;
  }
}
</style>
  
