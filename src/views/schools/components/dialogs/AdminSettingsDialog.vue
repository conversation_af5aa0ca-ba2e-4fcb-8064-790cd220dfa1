<template>
  <el-dialog
    title="管理员设置"
    v-model="dialogVisible"
    width="50%"
    :before-close="handleClose"
    draggable
    destroy-on-close
  >
    <!-- 主弹窗内容 -->
    <div class="admin-settings-content">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @submit.prevent>
          <el-form-item label="关键字:">
            <el-input
              v-model="searchForm.keyword"
              placeholder="姓名/手机号"
              @keydown.enter.prevent="handleSearch"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <el-button type="primary" @click="openAddAdminDialog" plain
          >新增管理员</el-button
        >
        <el-button type="info" @click="handleSelectExisting" plain
          >选择已有老师</el-button
        >
      </div>

      <!-- 管理员列表表格 -->
      <el-table
        :data="adminList"
        style="width: 100%; margin-top: 1.25rem"
        v-loading="adminLoading"
      >
        <el-table-column prop="nickName" label="姓名"></el-table-column>
        <el-table-column prop="phonenumber" label="手机号"></el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button
              link
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        style="margin-top: 1.25rem"
        v-model:current-page="adminPagination.pageNum"
        v-model:page-size="adminPagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="adminPagination.total"
        @size-change="fetchAdminList"
        @current-change="fetchAdminList"
      />
    </div>

    <!-- 新增管理员的二级弹窗 -->
    <el-dialog
      title="添加管理员"
      v-model="addAdminDialogVisible"
      width="40%"
      append-to-body
      :before-close="handleAddAdminClose"
      draggable
      destroy-on-close
    >
      <el-form
        :model="newAdminForm"
        :rules="newAdminRules"
        ref="newAdminFormRef"
        label-width="5rem"
        @submit.prevent
      >
        <el-form-item label="姓名" prop="nickName">
          <el-input
            v-model="newAdminForm.nickName"
            placeholder="请输入姓名"
          ></el-input>
          <!-- 占位符更具体 -->
        </el-form-item>
        <el-form-item label="手机号" prop="phonenumber">
          <el-input
            v-model="newAdminForm.phonenumber"
            placeholder="请输入手机号"
            @keydown.enter.prevent="submitNewAdmin"
          ></el-input>
          <!-- 占位符更具体 -->
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleAddAdminClose">取消</el-button>
          <el-button type="primary" @click="submitNewAdmin">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 选择已有老师的二级弹窗 -->
    <el-dialog
      title="已有老师列表"
      v-model="selectTeacherDialogVisible"
      width="40%"
      append-to-body
      :before-close="handleSelectTeacherClose"
      draggable
      destroy-on-close
    >
      <div class="select-teacher-content">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form
            :inline="true"
            :model="selectTeacherSearchForm"
            class="demo-form-inline"
            @submit.prevent
          >
            <el-form-item label="关键字:">
              <el-input
                v-model="selectTeacherSearchForm.keyword"
                placeholder="姓名/手机号"
                @keydown.enter.prevent="handleSelectTeacherSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSelectTeacherSearch"
                >查询</el-button
              >
              <el-button @click="handleSelectTeacherReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 老师列表 -->
        <el-table
          :data="teacherList"
          style="width: 100%; margin-top: 1.25rem"
          v-loading="teacherLoading"
        >
          <el-table-column width="50" align="center">
            <template #default="scope">
              <el-checkbox v-model="selectedTeachers" :label="scope.row.userId"
                >&nbsp;</el-checkbox
              >
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="姓名"></el-table-column>
          <el-table-column prop="sex" label="性别">
            <template #default="scope">
              {{
                scope.row.sex == 0 ? "男" : scope.row.sex == 1 ? "女" : "未知"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="phonenumber" label="手机号"></el-table-column>

          <el-table-column label="执教班级">
            <template #default="scope">
              <div v-if="Array.isArray(scope.row.depts)">
                <div v-for="dept in scope.row.depts" :key="dept.deptId">
                  {{ dept.deptName }}
                </div>
              </div>
              <div v-else>
                {{ scope.row.deptName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="subjectTaught" label="执教科目">
            <template #default="scope">
              <div
                v-for="item in scope.row.subjectTaught?.split(',')"
                :key="item"
              >
                <dict-tag :options="subject_taught" :value="item" />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleSelectTeacherClose">取消</el-button>
          <el-button type="primary" @click="submitSelectTeacher"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, reactive } from "vue"; // 引入 reactive
import { getCurrentInstance } from "vue";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();
const { subject_taught } = proxy.useDict("subject_taught");

/*
  1、将教师授权为管理员
  2、取消教师授权为管理员
*/
import { authUserSelectAll, authUserCancel } from "@/api/system/role";

// 根据学校ID 查询教师列表
import {
  getTeacherListBySchoolId,
  getSchoolDeptAndPermission,
} from "@/api/school";

// 引入新增和删除用户接口
import { addUser, delUser } from "@/api/system/user";
import { allocatedUserList } from "@/api/system/role";

const props = defineProps({
  visible: Boolean,
  dialogData: Object,
});
const emit = defineEmits(["close"]);

const dialogVisible = ref(props.visible);
const addAdminDialogVisible = ref(false); // 控制新增管理员弹窗的显示
const selectTeacherDialogVisible = ref(false); // 控制选择老师弹窗的显示

// 搜索表单数据
const searchForm = reactive({
  keyword: "",
});

// 管理员分页
const adminPagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  roleId: "",
});

// 加载状态
const adminLoading = ref(false);
const teacherLoading = ref(false);

// 选择老师弹窗的搜索表单
const selectTeacherSearchForm = reactive({
  keyword: "",
});

// 管理员列表数据
const adminList = ref([]);
// const allAdminList = ref([]); // 用于存储所有管理员，用于前端搜索

// 老师列表数据
const teacherList = ref([]);
const allTeacherList = ref([]); // 用于存储所有老师，用于前端搜索

const selectedTeachers = ref([]); // 用于存储选中的老师ID

// 新增管理员表单
const newAdminFormRef = ref(null); // 表单引用

// 新增管理员表单数据
const newAdminForm = reactive({
  nickName: "",
  phonenumber: "",
  userName: "", // 和 phonenumber 一样
  sex: "2", // 2 未知
  password: "123456",
  roleIds: [], // 管理员角色ID
  userType: "1", // 1 管理员
});

// 新增管理员表单验证规则
const newAdminRules = reactive({
  nickName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  phonenumber: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号格式",
      trigger: "blur",
    },
  ],
});

// 获取老师列表
const fetchTeacherList = async (schoolId) => {
  teacherLoading.value = true;
  try {
    const { code, data } = await getTeacherListBySchoolId(schoolId, adminPagination.value.roleId);
    if (code === 200) {
      teacherList.value = data;
      allTeacherList.value = JSON.parse(JSON.stringify(data)); // 深拷贝一份，防止意外修改
      console.log("老师列表", teacherList.value);
    }
  } catch (error) {
    console.error("获取老师列表失败:", error);
  } finally {
    teacherLoading.value = false;
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  emit("close");
};

const submitForm = () => {
  handleClose();
};

// 查询管理员
const handleSearch = () => {
  adminPagination.pageNum = 1;
  fetchAdminList();
};

// 重置搜索 表单
const handleReset = () => {
  console.log("重置搜索");
  resetSearchForm();
  adminPagination.pageNum = 1; // 重置时回到第一页
  fetchAdminList();
};

// 重置搜索 表单
const resetSearchForm = () => {
  searchForm.keyword = "";
};

// --- 管理员列表操作方法 ---
/**
 * 删除/取消管理员角色
 * @param {Object} row 当前行数据
 * 当 row.subjectTaught 有值时，表示该用户仍有执教科目，只移除其管理员角色；
 * 否则直接删除该用户信息。
 * 目前按照注释里的逻辑进行硬编码判断，后续可根据实际字段替换条件。
 */
const handleDelete = async (row) => {
  try {
    let response;
    // 如果有执教科目 => 移除角色授权
    if (row && row.subjectTaught) {
      response = await authUserCancel({
        roleId: adminPagination.value.roleId,
        userId: row.userId,
      });
    } else {
      // 否则 => 删除用户
      response = await delUser(row.userId);
    }

    const { code, data } = response || {};
    if (code === 200) {
      ElMessage.success(
        row && row.subjectTaught ? "移除管理员角色成功" : "删除管理员成功"
      );
      console.log(
        row && row.subjectTaught ? "移除管理员角色成功" : "删除管理员成功",
        data
      );
      fetchAdminList();
    }
  } catch (error) {
    console.log("handleDelete error:", error);
  }
};

// 选择已有老师
const handleSelectExisting = () => {
  // 打开选择已有老师的弹窗
  selectTeacherDialogVisible.value = true;
  fetchTeacherList(props.dialogData.schoolId);
};

// --- 新增管理员二级弹窗相关方法 ---
// 打开新增管理员弹窗
const openAddAdminDialog = () => {
  addAdminDialogVisible.value = true;
};

// 关闭新增管理员弹窗
const handleAddAdminClose = () => {
  addAdminDialogVisible.value = false;
  if (newAdminFormRef.value) {
    newAdminFormRef.value.clearValidate();
  }
  resetNewAdminForm(); // 关闭时清空表单
};

// 提交新增管理员
const submitNewAdmin = async () => {
  if (!newAdminFormRef.value) return;
  await newAdminFormRef.value.validate(async (valid) => {
    if (valid) {
      const formData = {
        ...newAdminForm,
        ...props.dialogData,
        userName: newAdminForm.phonenumber,
        roleIds: [adminPagination.value.roleId],
        deptIds:adminPagination.value.deptIds
      };
      const { code, data } = await addUser(formData);
      if (code === 200) {
        console.log("新增管理员成功", data);
        ElMessage.success("新增管理员成功");
        fetchAdminList();
        handleAddAdminClose();
      }
    }
  });
};

// 重置新增管理员表单
const resetNewAdminForm = () => {
  newAdminForm.nickName = "";
  newAdminForm.phonenumber = "";
  newAdminForm.userName = "";
  newAdminForm.sex = "2";
  newAdminForm.password = "123456";
  newAdminForm.userType = "1"; // 1 管理员
  newAdminForm.roleIds = [];
};

// --- 选择已有老师二级弹窗相关方法 ---

// 关闭选择老师弹窗
const handleSelectTeacherClose = () => {
  selectTeacherDialogVisible.value = false;
  selectTeacherSearchForm.keyword = "";
  selectedTeachers.value = [];
};

// 提交选择的老师
const submitSelectTeacher = async () => {
  if (selectedTeachers.value.length === 0) {
    ElMessage.warning("请至少选择一位老师");
    return;
  }
  const userIds = selectedTeachers.value.join(",");

  const { code, data } = await authUserSelectAll({
    roleId: adminPagination.value.roleId,
    userIds: userIds,
  });
  if (code === 200) {
    console.log("授权成功", data);
    ElMessage.success("授权成功");
    handleSelectTeacherClose();
    fetchAdminList(); // 刷新管理员列表
  }
};

// 获取学校部门和权限
const fetchSchoolDeptAndPermission = async (schoolId) => {
  const { code, data } = await getSchoolDeptAndPermission(schoolId);
  if (code == 200) {
      adminPagination.value.roleId = data.roleId;
    fetchAdminList();
  }
};

// 搜索老师
const handleSelectTeacherSearch = () => { 
  const keyword = selectTeacherSearchForm.keyword.trim().toLowerCase();
  if (!keyword) {
    teacherList.value = allTeacherList.value;
    return;
  }
  teacherList.value = allTeacherList.value.filter(
    (teacher) =>
      (teacher.nickName && teacher.nickName.toLowerCase().includes(keyword)) ||
      (teacher.phonenumber &&
        teacher.phonenumber.toLowerCase().includes(keyword))
  );
};

// 重置老师搜索
const handleSelectTeacherReset = () => {
  selectTeacherSearchForm.keyword = "";
  teacherList.value = allTeacherList.value;
};

// --- 获取管理员列表 ---
const fetchAdminList = async () => {
  adminLoading.value = true;
  try {
    const { code, rows, total } = await allocatedUserList({
      pageNum: adminPagination.value.pageNum,
      pageSize: adminPagination.value.pageSize,
      roleId: adminPagination.value.roleId,
      // roleId: 101,
      nickName: searchForm.keyword,
      phonenumber: searchForm.keyword,
    });
    if (code === 200) {
      adminList.value = rows;
      adminPagination.value.total = total;
    }
  } catch (error) {
    console.error("获取管理员列表失败:", error);
  } finally {
    adminLoading.value = false;
  }
};

watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      fetchSchoolDeptAndPermission(props.dialogData.schoolId);
    } else {
      // 主弹窗关闭时，也确保关闭二级弹窗并清空表单
      addAdminDialogVisible.value = false;
      resetNewAdminForm();
      resetSearchForm();
    }
  }
);
</script>

<style lang="scss" scoped>
.admin-settings-content {
  padding: 0 1.25rem 1.25rem; /* 添加一些内边距 */
}
.demo-form-inline {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.search-area {
  margin-bottom: 1.25rem;
}

.action-buttons {
  margin-bottom: 1.25rem;
}

.dialog-footer button:first-child {
  margin-right: .625rem;
}
</style>